class VehicleManager {
    static double max_speed = 100.0;
    static double min_speed = 0.0;

    static main() {
        let v1 = new Vehicle();
        let v2 = new Vehicle();

        //hard code vehicle actions
        v1.start();
        v1.direction = 100.0;
        v1.speed = 1.0;
        v1.stop();

        v2.start();
        v2.direction = 200.0;
        v2.speed = 60.0;
        v2.stop();
    }
}