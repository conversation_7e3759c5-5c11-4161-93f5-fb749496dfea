// JavaScript doesn't have a direct equivalent to Java's Scanner
// For user input in Node.js, you can use:
// const readline = require('readline');
// or for browser: prompt() function

// For Node.js environment, you would need to install readline-sync: npm install readline-sync
// const readlineSync = require('readline-sync');

class ProfileBuilder {
    static main() {
        console.log("this application will get your profile data and build a profile object");

        console.log("last name:");
        // In browser: const lastName = prompt("last name:");
        // In Node.js with readline-sync: const lastName = readlineSync.question("last name: ");

        console.log("first name:");
        // const firstName = prompt("first name:");
        // In Node.js with readline-sync: const firstName = readlineSync.question("first name: ");

        console.log("email:");
        // const email = prompt("email:");
        // In Node.js with readline-sync: const email = readlineSync.question("email: ");

        console.log("phone:");
        // const phone = prompt("phone:");
        // In Node.js with readline-sync: const phone = readlineSync.question("phone: ");

        // const profile = new Profile(lastName, firstName, email, phone);
        // console.log("profile created: " + profile);
    }
}

// To run the main function:
ProfileBuilder.main();